"""Streamlit application for digit detection."""

from digit_detection.adapters.custom_data_loader import CustomDataLoader
from digit_detection.adapters.mnist_data_loader import MNISTDataLoader
from digit_detection.custom_nn.neural_network import NeuralNetwork
from digit_detection.tensorflow_nn.neural_network import TensorFlowNeuralNetwork
import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Any
import os
from pathlib import Path

# Import adapters to register models and data loaders

from digit_detection.core.activator_type import ActivatorType
from digit_detection.core.cost_type import CostType
from digit_detection.core.dataset_type import DatasetType
from digit_detection.core.flavour_type import FlavourType
from digit_detection.core.model import ModelConfig, DataLoaderFactory
from digit_detection.core.model_factory import ModelFactory

ModelFactory.register(FlavourType.CUSTOM, NeuralNetwork)
ModelFactory.register(FlavourType.TENSORFLOW, TensorFlowNeuralNetwork)

# Register data loaders
DataLoaderFactory.register(DatasetType.MNIST, MNISTDataLoader)
DataLoaderFactory.register(DatasetType.CUSTOM, CustomDataLoader)

from digit_detection.custom_nn.epoch_data import EpochData
from digit_detection.core.projector import Projector, ProjectionMethod, ProjectionTarget, ProjectionDimension
from digit_detection.core.visualizer import create_streamlit_video

def create_sidebar() -> Dict[str, Any]:
    """Create the sidebar with model configuration options.

    Returns:
        Dictionary of configuration parameters
    """
    st.sidebar.header("Learning Machine")
    st.sidebar.markdown("*Digit Detection App*")
    st.sidebar.markdown("---")

    st.sidebar.subheader("Model Configuration")

    # Model flavor
    flavor = st.sidebar.selectbox(
        "Model Backend",
        options=[
            FlavourType.TENSORFLOW,
            #FlavourType.PYTORCH,
            FlavourType.CUSTOM
        ],
        format_func=lambda x: x.name.capitalize()
    )

    # Dataset
    dataset = st.sidebar.selectbox(
        "Dataset",
        options=[
            DatasetType.MNIST,
            DatasetType.CUSTOM
        ],
        format_func=lambda x: x.name
    )

    # Network architecture
    st.sidebar.subheader("Network Architecture")

    # Hidden layers
    num_hidden_layers = st.sidebar.slider("Number of Hidden Layers", 1, 5, 2)

    # Layer sizes
    layer_sizes = [784]  # Input layer (28x28 = 784)

    for i in range(num_hidden_layers):
        layer_size = st.sidebar.slider(
            f"Hidden Layer {i+1} Size",
            10,
            512,
            128 if i == 0 else layer_sizes[-1] // 2
        )
        layer_sizes.append(layer_size)

    layer_sizes.append(10)  # Output layer (10 digits)

    # Activator types
    activator_types = []

    for i in range(num_hidden_layers):
        activation = st.sidebar.selectbox(
            f"Hidden Layer {i+1} Activation",
            options=[
                ActivatorType.SIGMOID
            ],
            index=0,
            format_func=lambda x: x.name.capitalize()
        )
        activator_types.append(activation)

    # Output layer activation is always sigmoid for now
    activator_types.append(ActivatorType.SIGMOID)

    # Training parameters
    st.sidebar.subheader("Training Parameters")

    cost_function = st.sidebar.selectbox(
        "Cost Function",
        options=[
            CostType.MEAN_SQUARED_ERROR
        ],
        index=0,
        format_func=lambda x: x.name.replace("_", " ").title()
    )

    learning_rate = st.sidebar.slider(
        "Learning Rate",
        min_value=0.0001,
        max_value=0.1,
        value=0.001,
        step=0.0001,
        format="%.4f"
    )

    batch_size = st.sidebar.select_slider(
        "Batch Size",
        options=[16, 32, 64, 128, 256],
        value=32
    )

    epochs = st.sidebar.slider("Epochs", 1, 50, 10)

    random_seed = st.sidebar.number_input(
        "Random Seed",
        min_value=0,
        max_value=9999,
        value=42,
        step=1
    )

    return {
        "flavor": flavor,
        "dataset": dataset,
        "layer_sizes": layer_sizes,
        "activator_types": activator_types,
        "cost_function": cost_function,
        "learning_rate": learning_rate,
        "batch_size": batch_size,
        "epochs": epochs,
        "random_seed": random_seed
    }


def display_model_summary(config: Dict[str, Any]) -> None:
    """Display a summary of the model configuration.

    Args:
        config: Model configuration dictionary
    """
    st.subheader("Model Summary")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Backend:**")
        st.markdown("**Dataset:**")
        st.markdown("**Cost Function:**")
        st.markdown("**Learning Rate:**")
        st.markdown("**Batch Size:**")
        st.markdown("**Epochs:**")

    with col2:
        st.markdown(f"`{config['flavor'].name}`")
        st.markdown(f"`{config['dataset'].name}`")
        st.markdown(f"`{config['cost_function'].name.replace('_', ' ').title()}`")
        st.markdown(f"`{config['learning_rate']}`")
        st.markdown(f"`{config['batch_size']}`")
        st.markdown(f"`{config['epochs']}`")

    st.markdown("**Network Architecture:**")

    # Create a table for the network architecture
    architecture_data = []

    # Input layer
    architecture_data.append({
        "Layer": "Input",
        "Size": config['layer_sizes'][0],
        "Activation": "N/A"
    })

    # Hidden layers
    for i in range(len(config['layer_sizes']) - 2):
        architecture_data.append({
            "Layer": f"Hidden {i+1}",
            "Size": config['layer_sizes'][i+1],
            "Activation": config['activator_types'][i].name.capitalize()
        })

    # Output layer
    architecture_data.append({
        "Layer": "Output",
        "Size": config['layer_sizes'][-1],
        "Activation": config['activator_types'][-1].name.capitalize()
    })

    st.table(architecture_data)


def display_training_results(epochal_history: List[EpochData]) -> None:
    """Display training results with charts.

    Args:
        epochal_history: List of EpochData objects
    """
    st.subheader("Training Results")

    # Extract accuracy and cost from List[EpochData]
    epochs = list(range(1, len(epochal_history) + 1))
    accuracies = [epoch_data.accuracy for epoch_data in epochal_history]
    costs = [epoch_data.cost for epoch_data in epochal_history]

    # Create tabs for different metrics
    tab1, tab2 = st.tabs(["Accuracy", "Cost"])

    with tab1:
        # Plot accuracy
        fig, ax = plt.subplots(figsize=(10, 4))
        ax.plot(epochs, accuracies, label='Training Accuracy')
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Accuracy')
        ax.set_title('Model Accuracy')
        ax.legend()
        ax.grid(True)
        st.pyplot(fig)

    with tab2:
        # Plot cost
        fig, ax = plt.subplots(figsize=(10, 4))
        ax.plot(epochs, costs, label='Training Cost')
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Cost')
        ax.set_title('Model Cost')
        ax.legend()
        ax.grid(True)
        st.pyplot(fig)


def display_evaluation_results(metrics: Dict[str, Any]) -> None:
    """Display evaluation results.

    Args:
        metrics: Evaluation metrics dictionary
    """
    st.subheader("Evaluation Results")

    col1, col2 = st.columns(2)

    with col1:
        st.metric("Accuracy", f"{metrics['accuracy']:.4f}")
        if 'precision' in metrics['additional_metrics']:
            st.metric("Precision", f"{metrics['additional_metrics']['precision']:.4f}")

    with col2:
        st.metric("Loss", f"{metrics['loss']:.4f}")
        if 'recall' in metrics['additional_metrics']:
            st.metric("Recall", f"{metrics['additional_metrics']['recall']:.4f}")

    if metrics['confusion_matrix'] is not None:
        st.subheader("Confusion Matrix")
        fig, ax = plt.subplots(figsize=(10, 8))
        im = ax.imshow(metrics['confusion_matrix'], cmap='Blues')
        ax.set_xticks(np.arange(10))
        ax.set_yticks(np.arange(10))
        ax.set_xlabel('Predicted Label')
        ax.set_ylabel('True Label')
        ax.set_title('Confusion Matrix')

        # Add colorbar
        plt.colorbar(im)

        # Add text annotations
        for i in range(10):
            for j in range(10):
                ax.text(j, i, f"{metrics['confusion_matrix'][i, j]:.2f}",
                       ha="center", va="center", color="black" if metrics['confusion_matrix'][i, j] < 0.5 else "white")

        st.pyplot(fig)


def main() -> None:
    """Main function for the Streamlit application."""
    st.title("Learning Machine: Digit Detection")
    st.write("Train and evaluate neural networks for handwritten digit recognition")

    # Create sidebar and get configuration
    config_dict = create_sidebar()

    # Display model summary
    display_model_summary(config_dict)

    # Create tabs for different sections
    train_tab, test_tab, viz_tab, predict_tab = st.tabs(["Train", "Evaluate", "Visualize", "Predict"])

    with train_tab:
        st.subheader("Train Model")

        if st.button("Train Model", key="train_button"):
            with st.spinner("Loading data..."):
                # Load data
                data_loader = DataLoaderFactory.create_data_loader(config_dict["dataset"])
                data = data_loader.load_data()
                st.success(f"Data loaded: {data.x_train.shape[0]} training samples, {data.x_test.shape[0]} test samples")

            with st.spinner("Building model..."):
                # Create model configuration
                model_config = ModelConfig(
                    layer_sizes=config_dict["layer_sizes"],
                    activator_types=config_dict["activator_types"],
                    cost_type=config_dict["cost_function"],
                    learning_rate=config_dict["learning_rate"],
                    batch_size=config_dict["batch_size"],
                    epochs=config_dict["epochs"],
                    flavor=config_dict["flavor"],
                    random_seed=config_dict["random_seed"]
                )

                # Create and build model
                model = ModelFactory.create_model(model_config)
                st.success("Model built successfully")

            with st.spinner("Training model..."):
                # Train model
                history = model.train(
                    data.x_train,
                    data.y_train,
                    data.x_test,
                    data.y_test
                )
                st.success("Model trained successfully")

                # Store model in session state
                st.session_state.model = model
                st.session_state.data = data
                st.session_state.history = history

            # Display training results
            display_training_results(history)

    with test_tab:
        st.subheader("Evaluate Model")

        if st.button("Evaluate Model", key="evaluate_button"):
            if 'model' not in st.session_state or 'data' not in st.session_state:
                st.warning("Please train a model first")
            else:
                with st.spinner("Evaluating model..."):
                    # Evaluate model
                    metrics = st.session_state.model.evaluate(
                        st.session_state.data.x_test,
                        st.session_state.data.y_test
                    )
                    st.success("Model evaluated successfully")

                    # Store metrics in session state
                    st.session_state.metrics = {
                        'accuracy': metrics.accuracy,
                        'loss': metrics.loss,
                        'confusion_matrix': metrics.confusion_matrix,
                        'additional_metrics': metrics.additional_metrics
                    }

                # Display evaluation results
                display_evaluation_results(st.session_state.metrics)

    with viz_tab:
        st.subheader("Training Visualization")
        st.write("Create animated videos showing how the neural network learns to classify digits")

        if 'model' not in st.session_state or 'history' not in st.session_state:
            st.warning("Please train a model first to generate visualizations")
        else:
            # Visualization controls
            col1, col2 = st.columns(2)

            with col1:
                projection_method = st.selectbox(
                    "Projection Method",
                    options=[ProjectionMethod.PCA, ProjectionMethod.TSNE, ProjectionMethod.UMAP],
                    format_func=lambda x: x.name,
                    help="Method for dimensionality reduction"
                )

                use_3d = st.checkbox(
                    "3D Visualization",
                    value=False,
                    help="3D visualization is more visually appealing but slower to render"
                )

            with col2:
                quality = st.selectbox(
                    "Video Quality",
                    options=["low", "medium", "high"],
                    index=1,
                    help="Higher quality takes longer to render"
                )

                sample_count = st.slider(
                    "Sample Count",
                    min_value=100,
                    max_value=1000,
                    value=300,
                    step=50,
                    help="Number of data points to visualize (fewer = faster)"
                )

            if st.button("Generate Training Animation", type="primary"):
                try:
                    with st.spinner("Creating projections..."):
                        # Create projector from trained model
                        

                        # Create projection data
                        dimension = ProjectionDimension.THREE_D if use_3d else ProjectionDimension.TWO_D
                        projection_data = Projector().create_projection_data(
                            epoch_data=st.session_state.history,
                            target=ProjectionTarget.INPUT,
                            method=projection_method,
                            dimension=dimension,
                            sample_count=sample_count
                        )

                    with st.spinner("Generating animation... This may take a few minutes."):
                        # Create video
                        video_path = create_streamlit_video(
                            projection_data=projection_data,
                            use_3d=use_3d,
                            quality=quality
                        )

                        # Store video info in session state
                        st.session_state.video_path = video_path
                        st.session_state.video_info = {
                            'method': projection_method.name,
                            'use_3d': use_3d,
                            'quality': quality,
                            'sample_count': sample_count,
                            'epochs': len(projection_data)
                        }

                    st.success("Animation generated successfully!")

                except Exception as e:
                    st.error(f"Error generating animation: {str(e)}")
                    st.exception(e)

            # Display video if available
            if hasattr(st.session_state, 'video_path') and os.path.exists(st.session_state.video_path):
                st.subheader("Training Animation")

                # Show video info
                if hasattr(st.session_state, 'video_info'):
                    info = st.session_state.video_info
                    col1, col2, col3, col4 = st.columns(4)
                    with col1:
                        st.metric("Method", info['method'])
                    with col2:
                        st.metric("Dimension", "3D" if info['use_3d'] else "2D")
                    with col3:
                        st.metric("Quality", info['quality'].title())
                    with col4:
                        st.metric("Samples", info['sample_count'])

                # Video description
                st.markdown("""
                **How to interpret the animation:**
                - **Colors** represent true digit labels (0-9)
                - **White borders** indicate incorrect predictions
                - **Point movement** shows how the network learns to separate digit classes
                - **Accuracy graph** tracks improvement over training epochs
                """)

                # Display the video
                st.video(st.session_state.video_path)

                # File info and download
                video_path = Path(st.session_state.video_path)
                if video_path.exists():
                    file_size = video_path.stat().st_size / (1024 * 1024)  # MB
                    st.caption(f"Video file: {video_path.name} ({file_size:.1f} MB)")

                    # Download button
                    with open(st.session_state.video_path, 'rb') as video_file:
                        st.download_button(
                            label="Download Animation",
                            data=video_file.read(),
                            file_name="training_animation.mp4",
                            mime="video/mp4"
                        )

    with predict_tab:
        st.subheader("Predict Digits")
        st.write("Coming soon: Draw a digit or upload an image to test the model")


if __name__ == "__main__":
    main()
