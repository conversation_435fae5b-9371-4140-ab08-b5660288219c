
# class EpochProjectionAnimation(ThreeDScene if use_3d else Scene):
#     def construct(self):
#         data_dir = "mnist_viz_frames"
#         files = sorted([f for f in os.listdir(data_dir) if f.endswith(".pkl")])
#         all_data = [pickle.load(open(os.path.join(data_dir, f), 'rb')) for f in files]

#         colors = [RED, GREEN, BLUE, GOLD, PURPLE, ORANGE, TEAL, PINK, MAROON, GRAY]
#         dots = VGroup()

#         # Find bounding box for scaling
#         all_points = np.concatenate([d["x2d"] for d in all_data], axis=0)
#         max_abs = np.max(np.abs(all_points))
#         scale = 3.0 / max_abs  # auto-scale

#         # Legend for digit colors
#         legend = VGroup()
#         for i in range(10):
#             label = Text(str(i), font_size=20).set_color(colors[i])
#             label.next_to(legend, DOWN, buff=0.1) if i > 0 else None
#             legend.add(label)
#         legend.to_corner(UL)
#         self.add(legend)

#         # Initial dots with border highlighting incorrect predictions
#         init = all_data[0]
#         for i in range(len(init["x2d"])):
#             point = init["x2d"][i] * scale
#             label = init["labels"][i]
#             correct = init["correct"][i]

#             if use_3d:
#                 p = [*point, 0][:3]
#             else:
#                 p = [*point[:2], 0]

#             dot = Dot(point=p, radius=0.05, color=colors[label], stroke_color=WHITE if not correct else colors[label], stroke_width=2.5)
#             dot.set_opacity(1.0)
#             dots.add(dot)

#         self.play(FadeIn(dots))
#         self.wait(1)

#         # Animate transitions and accuracy
#         accuracy_vals = [all_data[0]["accuracy"]]
#         acc_graph = Axes(
#             x_range=[0, epochs, 1],
#             y_range=[0, 100, 10],
#             x_length=5,
#             y_length=3,
#             axis_config={"font_size": 16}
#         ).to_corner(DR)
#         self.add(acc_graph)

#         accuracy_curve = always_redraw(lambda: acc_graph.plot_line_graph(
#             x_values=list(range(1, len(accuracy_vals)+1)),
#             y_values=accuracy_vals,
#             add_vertex_dots=False,
#             line_color=YELLOW
#         ))
#         self.add(accuracy_curve)

#         for i in range(1, len(all_data)):
#             curr = all_data[i]
#             new_coords = curr["x2d"] * scale
#             correct = curr["correct"]
#             acc = curr["accuracy"]
#             epoch = curr["epoch"]
#             accuracy_vals.append(acc)

#             acc_text = Text(f"Epoch {epoch} Accuracy: {acc:.2f}%", font_size=24).to_corner(UR)

#             animations = []
#             for j, dot in enumerate(dots):
#                 point = new_coords[j]
#                 if use_3d:
#                     p = [*point, 0][:3]
#                 else:
#                     p = [*point[:2], 0]

#                 dot.set_color(colors[curr["labels"][j]])
#                 dot.set_stroke(color=WHITE if not correct[j] else colors[curr["labels"][j]], width=2.5)
#                 animations.append(dot.animate.move_to(p))

#             self.play(*animations, FadeIn(acc_text), run_time=1.5)
#             self.wait(0.5)
#             self.remove(acc_text)

#         self.wait(1)

# # --- RENDERER ---
# def render_scene(scene_class, output_file="output.mp4", quality="high"):
#     config.output_file = output_file
#     config.verbosity = "WARNING"
#     if quality == "low":
#         config.pixel_height = 480
#         config.pixel_width = 854
#         config.frame_rate = 15
#     elif quality == "high":
#         config.pixel_height = 1080
#         config.pixel_width = 1920
#         config.frame_rate = 60
#     scene = scene_class()
#     scene.render()
#     open_file(config.output_file)

# render_scene(EpochProjectionAnimation, quality="low")

class Visualizer:
    
    def __init__(
        self,
        epoch_data: List[EpochData]
    )