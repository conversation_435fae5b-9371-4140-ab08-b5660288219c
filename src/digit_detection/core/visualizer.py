from digit_detection.core.projector import ProjectionData
from digit_detection.custom_nn.epoch_data import EpochD<PERSON>
from typing import List, Optional
import numpy as np
import tempfile
import os
from pathlib import Path
from manim import (
    Scene, ThreeDScene, VGroup, Dot, Text, Axes, FadeIn, always_redraw, config,
    RED, GREEN, BLUE, GOLD, PURPLE, ORANGE, TEAL, PINK, MAROON, GRAY, WHITE, YELLOW,
    UL, UR, DR, DOWN
)

class Visualizer:

    def __init__(self, projection_data_frames: list[ProjectionData] = None, use_3d: bool = False, **kwargs):
        """
        Initializes the scene with a list of projection data frames.

        Args:
            projection_data_frames (list[ProjectionData], optional):
                A list of ProjectionData objects, each representing a frame/epoch.
                Defaults to an empty list.
            use_3d (bool, optional): Whether to interpret projected_data as 3D.
                                     Currently, the scene is set up for 2D visualization.
                                     Defaults to False.
        """
        super().__init__(**kwargs)
        self.all_data = projection_data_frames if projection_data_frames is not None else []
        self.use_3d = use_3d # This scene is primarily designed for 2D projection visualization

class ModelTrainingScene(Scene):

    def __init__(self, projection_data: List[ProjectionData], use_3d: bool = False, **kwargs):
        """Initialize the scene with projection data.

        Args:
            projection_data: List of projection data for each epoch
            use_3d: Whether to use 3D projection
            **kwargs: Additional arguments for Scene
        """
        super().__init__(**kwargs)
        self.projection_data = projection_data
        self.use_3d = use_3d

    def construct(self):
        """Construct the animation using projection_data instead of pickle files."""
        if not self.projection_data:
            return

        colors = [RED, GREEN, BLUE, GOLD, PURPLE, ORANGE, TEAL, PINK, MAROON, GRAY]
        dots = VGroup()

        # Find bounding box for scaling
        all_points = np.concatenate([data.projected_data for data in self.projection_data], axis=0)
        max_abs = np.max(np.abs(all_points))
        scale = 3.0 / max_abs if max_abs > 0 else 1.0

        # Legend for digit colors
        legend = VGroup()
        for i in range(10):
            label = Text(str(i), font_size=20).set_color(colors[i])
            if i > 0:
                label.next_to(legend, DOWN, buff=0.1)
            legend.add(label)
        legend.to_corner(UL)
        self.add(legend)

        # Initial dots with border highlighting incorrect predictions
        init_data = self.projection_data[0]
        for i in range(len(init_data.projected_data)):
            point = init_data.projected_data[i] * scale
            label = init_data.labels[i]
            correct = init_data.preds[i] == label

            if self.use_3d:
                p = [*point, 0][:3] if len(point) >= 2 else [point[0], 0, 0]
            else:
                p = [*point[:2], 0] if len(point) >= 2 else [point[0], 0, 0]

            dot = Dot(
                point=p,
                radius=0.05,
                color=colors[label],
                stroke_color=WHITE if not correct else colors[label],
                stroke_width=2.5 if not correct else 0
            )
            dot.set_opacity(1.0)
            dots.add(dot)

        self.play(FadeIn(dots))
        self.wait(1)

        # Animate transitions and accuracy
        accuracy_vals = [self.projection_data[0].accuracy]
        epochs = len(self.projection_data)
        acc_graph = Axes(
            x_range=[0, epochs, max(1, epochs // 10)],
            y_range=[0, 100, 10],
            x_length=5,
            y_length=3,
            axis_config={"font_size": 16}
        ).to_corner(DR)
        self.add(acc_graph)

        accuracy_curve = always_redraw(lambda: acc_graph.plot_line_graph(
            x_values=list(range(1, len(accuracy_vals)+1)),
            y_values=accuracy_vals,
            add_vertex_dots=False,
            line_color=YELLOW
        ) if len(accuracy_vals) > 1 else VGroup())
        self.add(accuracy_curve)

        for i in range(1, len(self.projection_data)):
            curr_data = self.projection_data[i]
            new_coords = curr_data.projected_data * scale
            correct = curr_data.preds == curr_data.labels
            acc = curr_data.accuracy
            epoch = curr_data.epoch
            accuracy_vals.append(acc)

            acc_text = Text(f"Epoch {epoch} Accuracy: {acc:.2f}%", font_size=24).to_corner(UR)

            animations = []
            for j, dot in enumerate(dots):
                point = new_coords[j]
                if self.use_3d:
                    p = [*point, 0][:3] if len(point) >= 2 else [point[0], 0, 0]
                else:
                    p = [*point[:2], 0] if len(point) >= 2 else [point[0], 0, 0]

                dot.set_color(colors[curr_data.labels[j]])
                dot.set_stroke(
                    color=WHITE if not correct[j] else colors[curr_data.labels[j]],
                    width=2.5 if not correct[j] else 0
                )
                animations.append(dot.animate.move_to(p))

            self.play(*animations, FadeIn(acc_text), run_time=1.5)
            self.wait(0.5)
            self.remove(acc_text)

        self.wait(1)


def render_training_scene(
    projection_data: List[ProjectionData],
    output_file: Optional[str] = None,
    quality: str = "medium",
    use_3d: bool = False
) -> str:
    """Render the training scene and return the video file path.

    Args:
        projection_data: List of projection data for each epoch
        output_file: Output file path (optional, will generate if None)
        quality: Video quality ('low', 'medium', 'high')
        use_3d: Whether to use 3D visualization

    Returns:
        Path to the generated video file
    """
    if not projection_data:
        raise ValueError("Projection data cannot be empty")

    # Generate output file if not provided
    if output_file is None:
        temp_dir = Path(tempfile.gettempdir()) / "manim_videos"
        temp_dir.mkdir(exist_ok=True)
        output_file = str(temp_dir / f"training_animation_{len(projection_data)}_epochs.mp4")

    # Ensure output directory exists
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Configure Manim
    config.output_file = str(output_file)
    config.verbosity = "WARNING"
    config.disable_caching = True

    if quality == "low":
        config.pixel_height = 480
        config.pixel_width = 854
        config.frame_rate = 15
    elif quality == "medium":
        config.pixel_height = 720
        config.pixel_width = 1280
        config.frame_rate = 24
    elif quality == "high":
        config.pixel_height = 1080
        config.pixel_width = 1920
        config.frame_rate = 30
    else:
        raise ValueError(f"Unsupported quality: {quality}")

    # Create and render scene
    scene = ModelTrainingScene(projection_data=projection_data, use_3d=use_3d)
    scene.render()

    return str(output_file)


def create_streamlit_video(
    projection_data: List[ProjectionData],
    use_3d: bool = False,
    quality: str = "medium"
) -> str:
    """Create a video optimized for Streamlit display.

    Args:
        projection_data: List of projection data for each epoch
        use_3d: Whether to use 3D visualization
        quality: Video quality ('low', 'medium', 'high')

    Returns:
        Path to the generated video file
    """
    # Create output directory in temp folder
    temp_dir = Path(tempfile.gettempdir()) / "streamlit_videos"
    temp_dir.mkdir(exist_ok=True)

    output_file = temp_dir / "training_visualization.mp4"

    return render_training_scene(
        projection_data=projection_data,
        output_file=str(output_file),
        quality=quality,
        use_3d=use_3d
    )