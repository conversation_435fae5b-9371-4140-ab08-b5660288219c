from digit_detection.core.projector import ProjectionData
from digit_detection.custom_nn.epoch_data import EpochData
from typing import List
import numpy as np
from manim import (
    Scene, ThreeDScene, VGroup, Dot, Text, Axes, FadeIn, always_redraw,
    RED, GREEN, BLUE, GOLD, PURPLE, ORANGE, TEAL, PINK, MAROON, GRAY, WHITE, YELLOW,
    UL, UR, DR, DOWN
)

class Visualizer:

    def __init__(
        self,
        projection_data: List[ProjectionData]
    ) -> None:

        pass

class ModelTrainingScene(Scene):

    def __init__(self, projection_data: List[ProjectionData], use_3d: bool = False, **kwargs):
        """Initialize the scene with projection data.

        Args:
            projection_data: List of projection data for each epoch
            use_3d: Whether to use 3D projection
            **kwargs: Additional arguments for Scene
        """
        super().__init__(**kwargs)
        self.projection_data = projection_data
        self.use_3d = use_3d

    def construct(self):
        """Construct the animation using projection_data instead of pickle files."""
        if not self.projection_data:
            return

        colors = [R<PERSON>, <PERSON>RE<PERSON>, BLUE, GOLD, PURPLE, ORANGE, TEAL, PINK, MARO<PERSON>, GRAY]
        dots = VGroup()

        # Find bounding box for scaling
        all_points = np.concatenate([data.projected_data for data in self.projection_data], axis=0)
        max_abs = np.max(np.abs(all_points))
        scale = 3.0 / max_abs if max_abs > 0 else 1.0

        # Legend for digit colors
        legend = VGroup()
        for i in range(10):
            label = Text(str(i), font_size=20).set_color(colors[i])
            if i > 0:
                label.next_to(legend, DOWN, buff=0.1)
            legend.add(label)
        legend.to_corner(UL)
        self.add(legend)

        # Initial dots with border highlighting incorrect predictions
        init_data = self.projection_data[0]
        for i in range(len(init_data.projected_data)):
            point = init_data.projected_data[i] * scale
            label = init_data.labels[i]
            correct = init_data.preds[i] == label

            if self.use_3d:
                p = [*point, 0][:3] if len(point) >= 2 else [point[0], 0, 0]
            else:
                p = [*point[:2], 0] if len(point) >= 2 else [point[0], 0, 0]

            dot = Dot(
                point=p,
                radius=0.05,
                color=colors[label],
                stroke_color=WHITE if not correct else colors[label],
                stroke_width=2.5 if not correct else 0
            )
            dot.set_opacity(1.0)
            dots.add(dot)

        self.play(FadeIn(dots))
        self.wait(1)

        # Animate transitions and accuracy
        accuracy_vals = [self.projection_data[0].accuracy]
        epochs = len(self.projection_data)
        acc_graph = Axes(
            x_range=[0, epochs, max(1, epochs // 10)],
            y_range=[0, 100, 10],
            x_length=5,
            y_length=3,
            axis_config={"font_size": 16}
        ).to_corner(DR)
        self.add(acc_graph)

        accuracy_curve = always_redraw(lambda: acc_graph.plot_line_graph(
            x_values=list(range(1, len(accuracy_vals)+1)),
            y_values=accuracy_vals,
            add_vertex_dots=False,
            line_color=YELLOW
        ) if len(accuracy_vals) > 1 else VGroup())
        self.add(accuracy_curve)

        for i in range(1, len(self.projection_data)):
            curr_data = self.projection_data[i]
            new_coords = curr_data.projected_data * scale
            correct = curr_data.preds == curr_data.labels
            acc = curr_data.accuracy
            epoch = curr_data.epoch
            accuracy_vals.append(acc)

            acc_text = Text(f"Epoch {epoch} Accuracy: {acc:.2f}%", font_size=24).to_corner(UR)

            animations = []
            for j, dot in enumerate(dots):
                point = new_coords[j]
                if self.use_3d:
                    p = [*point, 0][:3] if len(point) >= 2 else [point[0], 0, 0]
                else:
                    p = [*point[:2], 0] if len(point) >= 2 else [point[0], 0, 0]

                dot.set_color(colors[curr_data.labels[j]])
                dot.set_stroke(
                    color=WHITE if not correct[j] else colors[curr_data.labels[j]],
                    width=2.5 if not correct[j] else 0
                )
                animations.append(dot.animate.move_to(p))

            self.play(*animations, FadeIn(acc_text), run_time=1.5)
            self.wait(0.5)
            self.remove(acc_text)

        self.wait(1)