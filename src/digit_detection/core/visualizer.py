from digit_detection.core.projector import ProjectionData
from digit_detection.custom_nn.epoch_data import EpochData
from typing import List, Optional
import numpy as np
import tempfile
import os
from pathlib import Path
from manim import (
    Scene, ThreeDScene, VGroup, Dot, Text, Axes, FadeIn, always_redraw, config,
    RED, GREEN, BLUE, GOLD, PURPLE, ORANGE, TEAL, PINK, MAROON, GRAY, WHITE, YELLOW,
    UL, UR, DR, DOWN
)

class Visualizer:

    def __init__(self, projection_data_frames: list[ProjectionData] = None, use_3d: bool = False, **kwargs):
        """
        Initializes the scene with a list of projection data frames.

        Args:
            projection_data_frames (list[ProjectionData], optional):
                A list of ProjectionData objects, each representing a frame/epoch.
                Defaults to an empty list.
            use_3d (bool, optional): Whether to interpret projected_data as 3D.
                                     Currently, the scene is set up for 2D visualization.
                                     Defaults to False.
        """
        super().__init__(**kwargs)
        self.all_data = projection_data_frames if projection_data_frames is not None else []
        self.use_3d = use_3d # This scene is primarily designed for 2D projection visualization

class ModelTrainingScene(Scene):
    """Manim scene for visualizing neural network training progress through projection data."""

    # Class constants
    DIGIT_COLORS = [RED, GREEN, BLUE, GOLD, PURPLE, ORANGE, TEAL, PINK, MAROON, GRAY]
    DOT_RADIUS = 0.05
    INCORRECT_STROKE_WIDTH = 2.5
    ANIMATION_RUNTIME = 1.5
    WAIT_TIME = 0.5
    FINAL_WAIT_TIME = 1.0

    def __init__(self, projection_data: List[ProjectionData], use_3d: bool = False, **kwargs):
        """Initialize the scene with projection data.

        Args:
            projection_data: List of projection data for each epoch
            use_3d: Whether to use 3D projection
            **kwargs: Additional arguments for Scene
        """
        super().__init__(**kwargs)
        self._validate_input_data(projection_data)
        self.projection_data = projection_data
        self.use_3d = use_3d
        self.scale_factor = self._calculate_scale_factor()

    def _validate_input_data(self, projection_data: List[ProjectionData]) -> None:
        """Validate the input projection data.

        Args:
            projection_data: List of projection data to validate

        Raises:
            ValueError: If projection data is empty or invalid
        """
        if not projection_data:
            raise ValueError("Projection data cannot be empty")

        if not all(isinstance(data, ProjectionData) for data in projection_data):
            raise ValueError("All items must be ProjectionData instances")

    def _calculate_scale_factor(self) -> float:
        """Calculate the scale factor for normalizing projection coordinates.

        Returns:
            Scale factor for coordinate normalization
        """
        all_points = np.concatenate([data.projected_data for data in self.projection_data], axis=0)
        max_abs = np.max(np.abs(all_points))
        return 3.0 / max_abs if max_abs > 0 else 1.0

    def _create_digit_legend(self) -> VGroup:
        """Create a legend showing digit colors.

        Returns:
            VGroup containing the legend elements
        """
        legend = VGroup()
        for digit in range(10):
            label = Text(str(digit), font_size=20).set_color(self.DIGIT_COLORS[digit])
            if digit > 0:
                label.next_to(legend, DOWN, buff=0.1)
            legend.add(label)
        legend.to_corner(UL)
        return legend

    def _create_accuracy_graph(self) -> Axes:
        """Create the accuracy tracking graph.

        Returns:
            Axes object for the accuracy graph
        """
        epochs = len(self.projection_data)
        return Axes(
            x_range=[0, epochs, max(1, epochs // 10)],
            y_range=[0, 100, 10],
            x_length=5,
            y_length=3,
            axis_config={"font_size": 16}
        ).to_corner(DR)

    def _convert_to_scene_coordinates(self, point: np.ndarray) -> list:
        """Convert projection coordinates to scene coordinates.

        Args:
            point: Projection coordinates

        Returns:
            Scene coordinates as [x, y, z] list
        """
        scaled_point = point * self.scale_factor

        if self.use_3d:
            if len(scaled_point) >= 3:
                return scaled_point[:3].tolist()
            elif len(scaled_point) >= 2:
                return [scaled_point[0], scaled_point[1], 0]
            else:
                return [scaled_point[0], 0, 0]
        else:
            if len(scaled_point) >= 2:
                return [scaled_point[0], scaled_point[1], 0]
            else:
                return [scaled_point[0], 0, 0]

    def _create_data_point_dot(self, point: np.ndarray, true_label: int, predicted_label: int) -> Dot:
        """Create a dot representing a data point.

        Args:
            point: Projection coordinates
            true_label: True digit label
            predicted_label: Predicted digit label

        Returns:
            Dot object representing the data point
        """
        is_correct = predicted_label == true_label
        scene_coords = self._convert_to_scene_coordinates(point)

        return Dot(
            point=scene_coords,
            radius=self.DOT_RADIUS,
            color=self.DIGIT_COLORS[true_label],
            stroke_color=self.DIGIT_COLORS[true_label] if is_correct else WHITE,
            stroke_width=0 if is_correct else self.INCORRECT_STROKE_WIDTH
        ).set_opacity(1.0)

    def _create_initial_dots(self) -> VGroup:
        """Create initial dots for the first epoch.

        Returns:
            VGroup containing all initial dots
        """
        dots = VGroup()
        initial_data = self.projection_data[0]

        for i, point in enumerate(initial_data.projected_data):
            true_label = initial_data.labels[i]
            predicted_label = initial_data.preds[i]

            dot = self._create_data_point_dot(point, true_label, predicted_label)
            dots.add(dot)

        return dots

    def _create_accuracy_curve(self, acc_graph: Axes, accuracy_values: List[float]) -> VGroup:
        """Create the accuracy curve for the graph.

        Args:
            acc_graph: Axes object for the graph
            accuracy_values: List of accuracy values

        Returns:
            VGroup containing the accuracy curve
        """
        if len(accuracy_values) < 2:
            return VGroup()

        return acc_graph.plot_line_graph(
            x_values=list(range(1, len(accuracy_values) + 1)),
            y_values=accuracy_values,
            add_vertex_dots=False,
            line_color=YELLOW
        )

    def _create_epoch_text(self, epoch: int, accuracy: float) -> Text:
        """Create epoch information text.

        Args:
            epoch: Current epoch number
            accuracy: Current accuracy value

        Returns:
            Text object displaying epoch information
        """
        return Text(
            f"Epoch {epoch} Accuracy: {accuracy:.2f}%",
            font_size=24
        ).to_corner(UR)

    def _update_dot_for_epoch(self, dot: Dot, point: np.ndarray, true_label: int,
                             predicted_label: int) -> None:
        """Update a dot's properties for a new epoch.

        Args:
            dot: Dot to update
            point: New projection coordinates
            true_label: True digit label
            predicted_label: Predicted digit label
        """
        is_correct = predicted_label == true_label
        scene_coords = self._convert_to_scene_coordinates(point)

        dot.set_color(self.DIGIT_COLORS[true_label])
        dot.set_stroke(
            color=self.DIGIT_COLORS[true_label] if is_correct else WHITE,
            width=0 if is_correct else self.INCORRECT_STROKE_WIDTH
        )

        return dot.animate.move_to(scene_coords)

    def _animate_epoch_transition(self, dots: VGroup, epoch_data: ProjectionData) -> List:
        """Create animations for transitioning to a new epoch.

        Args:
            dots: VGroup of dots to animate
            epoch_data: Data for the current epoch

        Returns:
            List of animations to play
        """
        animations = []

        for i, dot in enumerate(dots):
            point = epoch_data.projected_data[i]
            true_label = epoch_data.labels[i]
            predicted_label = epoch_data.preds[i]

            animation = self._update_dot_for_epoch(dot, point, true_label, predicted_label)
            animations.append(animation)

        return animations

    def construct(self) -> None:
        """Construct the complete animation sequence."""
        # Setup phase
        legend = self._create_digit_legend()
        self.add(legend)

        accuracy_graph = self._create_accuracy_graph()
        self.add(accuracy_graph)

        # Initialize dots and show them
        dots = self._create_initial_dots()
        self.play(FadeIn(dots))
        self.wait(self.WAIT_TIME)

        # Setup accuracy tracking
        accuracy_values = [self.projection_data[0].accuracy]
        accuracy_curve = always_redraw(
            lambda: self._create_accuracy_curve(accuracy_graph, accuracy_values)
        )
        self.add(accuracy_curve)

        # Animate through epochs
        self._animate_training_epochs(dots, accuracy_values)

        # Final pause
        self.wait(self.FINAL_WAIT_TIME)

    def _animate_training_epochs(self, dots: VGroup, accuracy_values: List[float]) -> None:
        """Animate through all training epochs.

        Args:
            dots: VGroup of dots to animate
            accuracy_values: List to track accuracy values (modified in place)
        """
        for epoch_index in range(1, len(self.projection_data)):
            current_epoch_data = self.projection_data[epoch_index]
            accuracy_values.append(current_epoch_data.accuracy)

            # Create epoch information text
            epoch_text = self._create_epoch_text(
                current_epoch_data.epoch,
                current_epoch_data.accuracy
            )

            # Create animations for this epoch
            dot_animations = self._animate_epoch_transition(dots, current_epoch_data)
            all_animations = dot_animations + [FadeIn(epoch_text)]

            # Play animations
            self.play(*all_animations, run_time=self.ANIMATION_RUNTIME)
            self.wait(self.WAIT_TIME)
            self.remove(epoch_text)


def render_training_scene(
    projection_data: List[ProjectionData],
    output_file: Optional[str] = None,
    quality: str = "medium",
    use_3d: bool = False
) -> str:
    """Render the training scene and return the video file path.

    Args:
        projection_data: List of projection data for each epoch
        output_file: Output file path (optional, will generate if None)
        quality: Video quality ('low', 'medium', 'high')
        use_3d: Whether to use 3D visualization

    Returns:
        Path to the generated video file
    """
    if not projection_data:
        raise ValueError("Projection data cannot be empty")

    # Generate output file if not provided
    if output_file is None:
        temp_dir = Path(tempfile.gettempdir()) / "manim_videos"
        temp_dir.mkdir(exist_ok=True)
        output_file = str(temp_dir / f"training_animation_{len(projection_data)}_epochs.mp4")

    # Ensure output directory exists
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    # Configure Manim
    config.output_file = str(output_file)
    config.verbosity = "WARNING"
    config.disable_caching = True

    if quality == "low":
        config.pixel_height = 480
        config.pixel_width = 854
        config.frame_rate = 15
    elif quality == "medium":
        config.pixel_height = 720
        config.pixel_width = 1280
        config.frame_rate = 24
    elif quality == "high":
        config.pixel_height = 1080
        config.pixel_width = 1920
        config.frame_rate = 30
    else:
        raise ValueError(f"Unsupported quality: {quality}")

    # Create and render scene
    scene = ModelTrainingScene(projection_data=projection_data, use_3d=use_3d)
    scene.render()

    return str(output_file)


def create_streamlit_video(
    projection_data: List[ProjectionData],
    use_3d: bool = False,
    quality: str = "medium"
) -> str:
    """Create a video optimized for Streamlit display.

    Args:
        projection_data: List of projection data for each epoch
        use_3d: Whether to use 3D visualization
        quality: Video quality ('low', 'medium', 'high')

    Returns:
        Path to the generated video file
    """
    # Create output directory in temp folder
    temp_dir = Path(tempfile.gettempdir()) / "streamlit_videos"
    temp_dir.mkdir(exist_ok=True)

    output_file = temp_dir / "training_visualization.mp4"

    return render_training_scene(
        projection_data=projection_data,
        output_file=str(output_file),
        quality=quality,
        use_3d=use_3d
    )